/**
 * Authentication Flow Test Script
 * 
 * This script tests the complete authentication flow including:
 * - User registration
 * - Email verification
 * - Login/logout
 * - Profile management
 * - Role-based access control
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testAuthFlow() {
  console.log('🚀 Starting Authentication Flow Test...\n');

  try {
    // Test 1: Create test user
    console.log('1. Testing user registration...');
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    
    const { data: signUpData, error: signUpError } = await supabase.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true,
      user_metadata: {
        first_name: 'Test',
        last_name: 'User',
        organization: 'Test Aviation Corp'
      }
    });

    if (signUpError) {
      console.error('❌ Registration failed:', signUpError.message);
      return;
    }

    console.log('✅ User registered successfully');
    const userId = signUpData.user.id;

    // Test 2: Verify user record was created
    console.log('\n2. Testing user record creation...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('❌ User record not found:', userError.message);
      return;
    }

    console.log('✅ User record created:', {
      id: userData.id,
      email: userData.email,
      role: userData.role
    });

    // Test 3: Verify user profile was created
    console.log('\n3. Testing user profile creation...');
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (profileError) {
      console.error('❌ User profile not found:', profileError.message);
      return;
    }

    console.log('✅ User profile created:', {
      first_name: profileData.first_name,
      last_name: profileData.last_name,
      organization: profileData.organization
    });

    // Test 4: Test role-based permissions
    console.log('\n4. Testing role-based permissions...');
    
    // Test operator permissions (default role)
    const { data: operatorFiles, error: operatorError } = await supabase
      .from('file_uploads')
      .select('*')
      .eq('user_id', userId);

    if (operatorError && !operatorError.message.includes('permission denied')) {
      console.error('❌ Unexpected error testing operator permissions:', operatorError.message);
      return;
    }

    console.log('✅ Operator permissions working (can access own files)');

    // Test 5: Update user role to inspector
    console.log('\n5. Testing role update...');
    const { error: roleUpdateError } = await supabase
      .from('users')
      .update({ role: 'inspector' })
      .eq('id', userId);

    if (roleUpdateError) {
      console.error('❌ Role update failed:', roleUpdateError.message);
      return;
    }

    console.log('✅ User role updated to inspector');

    // Test 6: Test file upload record creation
    console.log('\n6. Testing file upload record creation...');
    const { data: uploadData, error: uploadError } = await supabase
      .from('file_uploads')
      .insert({
        user_id: userId,
        file_name: 'test-mel-document.pdf',
        file_size: 1024000,
        file_type: 'application/pdf',
        file_url: `${userId}/mel/test-document.pdf`,
        upload_status: 'completed',
        metadata: {
          document_type: 'MEL',
          aircraft_type: 'A320'
        }
      })
      .select()
      .single();

    if (uploadError) {
      console.error('❌ File upload record creation failed:', uploadError.message);
      return;
    }

    console.log('✅ File upload record created:', {
      id: uploadData.id,
      file_name: uploadData.file_name,
      status: uploadData.upload_status
    });

    // Test 7: Test MEL document creation
    console.log('\n7. Testing MEL document creation...');
    const { data: melData, error: melError } = await supabase
      .from('mel_documents')
      .insert({
        file_upload_id: uploadData.id,
        document_type: 'MEL',
        aircraft_type: 'A320',
        version: '1.0',
        effective_date: '2024-01-01'
      })
      .select()
      .single();

    if (melError) {
      console.error('❌ MEL document creation failed:', melError.message);
      return;
    }

    console.log('✅ MEL document created:', {
      id: melData.id,
      document_type: melData.document_type,
      aircraft_type: melData.aircraft_type
    });

    // Test 8: Test activity logging
    console.log('\n8. Testing activity logging...');
    const { error: activityError } = await supabase
      .from('activities')
      .insert({
        user_id: userId,
        type: 'test_activity',
        description: 'Authentication flow test completed',
        metadata: {
          test_timestamp: new Date().toISOString()
        }
      });

    if (activityError) {
      console.error('❌ Activity logging failed:', activityError.message);
      return;
    }

    console.log('✅ Activity logged successfully');

    // Test 9: Test data cleanup
    console.log('\n9. Cleaning up test data...');
    
    // Delete in correct order due to foreign key constraints
    await supabase.from('activities').delete().eq('user_id', userId);
    await supabase.from('mel_documents').delete().eq('id', melData.id);
    await supabase.from('file_uploads').delete().eq('id', uploadData.id);
    await supabase.from('user_profiles').delete().eq('user_id', userId);
    await supabase.from('users').delete().eq('id', userId);
    await supabase.auth.admin.deleteUser(userId);

    console.log('✅ Test data cleaned up');

    console.log('\n🎉 All authentication flow tests passed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error(error.stack);
  }
}

// Test database connection
async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count(*)')
      .limit(1);

    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    return false;
  }
}

// Test RLS policies
async function testRLSPolicies() {
  console.log('\n🔒 Testing Row Level Security policies...');
  
  try {
    // Create a client with anon key (simulating unauthenticated user)
    const anonClient = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
    
    // Try to access users table without authentication (should fail)
    const { data, error } = await anonClient
      .from('users')
      .select('*')
      .limit(1);

    if (error && error.message.includes('permission denied')) {
      console.log('✅ RLS policies are working (unauthenticated access denied)');
      return true;
    } else {
      console.error('❌ RLS policies not working properly');
      return false;
    }
  } catch (error) {
    console.error('❌ RLS test error:', error.message);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('MEL/MMEL Evaluation System - Authentication Flow Test');
  console.log('=' .repeat(60));
  
  // Test database connection first
  const dbConnected = await testDatabaseConnection();
  if (!dbConnected) {
    console.log('\n❌ Database connection failed. Please check your Supabase configuration.');
    process.exit(1);
  }

  // Test RLS policies
  const rlsWorking = await testRLSPolicies();
  if (!rlsWorking) {
    console.log('\n⚠️  RLS policies may not be configured correctly.');
  }

  // Run main authentication flow test
  await testAuthFlow();
  
  console.log('\n' + '=' .repeat(60));
  console.log('Test completed. Check the output above for any issues.');
}

// Run the tests
runTests().catch(console.error);
