/**
 * Upload Page
 * 
 * File upload interface for MEL/MMEL documents
 */

'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Navigation from '@/components/layout/Navigation';
import DualFileUpload from '@/components/upload/DualFileUpload';
import { Loader2, FileText, AlertCircle } from 'lucide-react';
import type { FileUpload, UploadProgress } from '@/types';

export default function UploadPage() {
  const { user, loading } = useAuth();
  const [uploadedFiles, setUploadedFiles] = useState<FileUpload[]>([]);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">Please sign in to upload documents.</p>
        </div>
      </div>
    );
  }

  const handleUploadComplete = (files: FileUpload[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
    setUploadProgress([]);
    setUploadError(null);
  };

  const handleUploadProgress = (progress: UploadProgress[]) => {
    setUploadProgress(progress);
  };

  const handleUploadError = (error: string) => {
    setUploadError(error);
    setUploadProgress([]);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">Upload Documents</h1>
            <p className="mt-2 text-gray-600">
              Upload your MEL and MMEL documents for AI-powered compliance analysis
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Upload Error */}
        {uploadError && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Upload Error</h3>
                <div className="mt-2 text-sm text-red-700">{uploadError}</div>
              </div>
            </div>
          </div>
        )}

        {/* Upload Component */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Upload MEL/MMEL Documents
          </h2>
          <DualFileUpload
            onUploadComplete={handleUploadComplete}
            onUploadProgress={handleUploadProgress}
            onUploadError={handleUploadError}
          />
        </div>

        {/* Upload Progress */}
        {uploadProgress.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Progress</h3>
            <div className="space-y-4">
              {uploadProgress.map((progress) => (
                <div key={progress.file_id} className="flex items-center">
                  <div className="flex-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Uploading...</span>
                      <span className="text-gray-900">{Math.round(progress.progress)}%</span>
                    </div>
                    <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress.progress}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Uploaded Files */}
        {uploadedFiles.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Recently Uploaded Files
            </h3>
            <div className="space-y-4">
              {uploadedFiles.map((file) => (
                <div key={file.id} className="flex items-center p-4 border border-gray-200 rounded-lg">
                  <FileText className="h-8 w-8 text-blue-500 mr-4" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{file.file_name}</h4>
                    <p className="text-sm text-gray-500">
                      {file.metadata.document_type} • {(file.file_size / 1024 / 1024).toFixed(2)} MB
                    </p>
                    <p className="text-xs text-gray-400">
                      Uploaded {new Date(file.created_at).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {file.upload_status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Upload Instructions</h3>
          <div className="text-blue-700 space-y-2">
            <p>• Upload both MEL and MMEL documents for comprehensive analysis</p>
            <p>• Supported formats: PDF, DOC, DOCX (up to 50MB each)</p>
            <p>• Ensure documents are clearly scanned and readable</p>
            <p>• Processing typically takes 2-5 minutes depending on document size</p>
            <p>• You'll receive an email notification when analysis is complete</p>
          </div>
        </div>
      </div>
    </div>
  );
}
