/**
 * Root Layout Component
 *
 * This is the main layout component that wraps all pages in the application.
 * It includes global styles, providers, and the Mina AI assistant.
 */

import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import MinaAvatar from "@/components/ai/MinaAvatar";
import { AuthProvider } from "@/contexts/AuthContext";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "MEL/MMEL Evaluation System",
  description: "AI-powered MEL/MMEL compliance analysis for aviation professionals",
  keywords: ["MEL", "MMEL", "aviation", "compliance", "AI", "analysis"],
  authors: [{ name: "Adapty Air" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <div className="min-h-screen bg-background">
            {children}

            {/* Mina AI Assistant - Available on all pages */}
            <MinaAvatar
              isVisible={true}
              onToggle={() => {}}
              context="global"
            />
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
