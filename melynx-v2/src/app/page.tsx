/**
 * Home Page
 *
 * Landing page for the MEL/MMEL Evaluation System.
 * Redirects authenticated users to dashboard, shows login for unauthenticated users.
 */

'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Plane, Shield, Zap, Users } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Plane className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">MEL/MMEL Evaluator</h1>
            </div>
            <div className="space-x-4">
              <Button variant="outline">Sign In</Button>
              <Button>Get Started</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            AI-Powered MEL/MMEL
            <span className="text-blue-600"> Compliance Analysis</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Streamline your aviation compliance workflow with intelligent document analysis,
            automated compliance checking, and expert AI assistance from Mina.
          </p>
          <div className="space-x-4">
            <Button size="lg" className="px-8 py-3">
              Start Free Analysis
            </Button>
            <Button variant="outline" size="lg" className="px-8 py-3">
              Watch Demo
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Lightning Fast Analysis</h3>
            <p className="text-gray-600">
              Upload your MEL and MMEL documents and get comprehensive compliance analysis in minutes, not hours.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Regulatory Compliance</h3>
            <p className="text-gray-600">
              Ensure your MEL documents meet all regulatory requirements with our expert AI analysis system.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Expert AI Assistant</h3>
            <p className="text-gray-600">
              Get instant help from Mina, your AI assistant specialized in aviation compliance and MEL/MMEL analysis.
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 bg-blue-600 rounded-2xl p-12 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Compliance Workflow?</h2>
          <p className="text-xl mb-8 opacity-90">
            Join aviation professionals who trust our AI-powered analysis for critical compliance decisions.
          </p>
          <Button size="lg" variant="secondary" className="px-8 py-3">
            Start Your Free Trial
          </Button>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Plane className="h-6 w-6 mr-2" />
              <span className="text-lg font-semibold">MEL/MMEL Evaluator</span>
            </div>
            <p className="text-gray-400">
              Built with ❤️ for the aviation industry by Adapty Air
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}
