/**
 * Dashboard Page
 * 
 * Main dashboard for authenticated users showing recent uploads,
 * analysis results, and quick actions.
 */

'use client';

import React from 'react';
import { Upload, FileText, BarChart3, Users, Clock, CheckCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { getUserDisplayName } from '@/lib/auth';
import Navigation from '@/components/layout/Navigation';

export default function DashboardPage() {
  const { user, loading } = useAuth();

  // TODO: Replace with actual data from Supabase
  const mockStats = {
    totalUploads: 24,
    pendingAnalyses: 3,
    completedAnalyses: 18,
    complianceScoreAvg: 87
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">Please sign in to access the dashboard.</p>
        </div>
      </div>
    );
  }

  const mockRecentActivities = [
    {
      id: '1',
      type: 'upload',
      description: 'Uploaded MEL document for A320',
      timestamp: '2 hours ago'
    },
    {
      id: '2',
      type: 'analysis',
      description: 'Completed compliance analysis for B737 MEL',
      timestamp: '4 hours ago'
    },
    {
      id: '3',
      type: 'upload',
      description: 'Uploaded MMEL document for A330',
      timestamp: '1 day ago'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="mt-2 text-gray-600">
              Welcome back, {getUserDisplayName(user)}! Here's your MEL/MMEL evaluation workspace.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Uploads</p>
                <p className="text-2xl font-semibold text-gray-900">{mockStats.totalUploads}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Analyses</p>
                <p className="text-2xl font-semibold text-gray-900">{mockStats.pendingAnalyses}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Completed</p>
                <p className="text-2xl font-semibold text-gray-900">{mockStats.completedAnalyses}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Avg Compliance</p>
                <p className="text-2xl font-semibold text-gray-900">{mockStats.complianceScoreAvg}%</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
                <div className="space-y-3">
                  <Button className="w-full justify-start" variant="outline">
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Documents
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Start Analysis
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    View Reports
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
                <div className="space-y-4">
                  {mockRecentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {activity.type === 'upload' ? (
                          <Upload className="h-5 w-5 text-blue-500" />
                        ) : (
                          <BarChart3 className="h-5 w-5 text-green-500" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-500">{activity.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Getting Started Guide */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Getting Started</h3>
          <p className="text-blue-700 mb-4">
            New to the MEL/MMEL evaluation system? Here's how to get started:
          </p>
          <ol className="list-decimal list-inside space-y-2 text-blue-700">
            <li>Upload your MEL and MMEL documents using the upload feature</li>
            <li>Wait for automatic processing and metadata extraction</li>
            <li>Run AI-powered compliance analysis</li>
            <li>Review results and recommendations</li>
            <li>Export reports for your records</li>
          </ol>
          <div className="mt-4">
            <Button>
              Start Upload Process
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
