/**
 * User Profile Component
 * 
 * Handles user profile display, editing, and management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { User, Edit3, Save, X, Upload, Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { getCurrentUserWithProfile, getRoleDisplayName } from '@/lib/auth';
import { supabase } from '@/lib/supabase/client';
import { cn } from '@/lib/utils';
import type { User as UserType } from '@/types';

// Profile form validation schema
const profileSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  organization: z.string().optional(),
  phone: z.string().optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

interface UserProfileProps {
  className?: string;
}

export default function UserProfile({ className }: UserProfileProps) {
  const [user, setUser] = useState<UserType | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  /**
   * Load user profile on component mount
   */
  useEffect(() => {
    loadUserProfile();
  }, []);

  /**
   * Load current user profile
   */
  const loadUserProfile = async () => {
    try {
      setIsLoading(true);
      const currentUser = await getCurrentUserWithProfile();
      
      if (currentUser) {
        setUser(currentUser);
        reset({
          firstName: currentUser.profile.first_name,
          lastName: currentUser.profile.last_name,
          organization: currentUser.profile.organization || '',
          phone: currentUser.profile.phone || '',
        });
      }
    } catch (error) {
      setError('Failed to load user profile');
      console.error('Error loading user profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle profile form submission
   */
  const onSubmit = async (data: ProfileForm) => {
    if (!user) return;

    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          first_name: data.firstName,
          last_name: data.lastName,
          organization: data.organization || null,
          phone: data.phone || null,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (updateError) {
        throw updateError;
      }

      // Update local user state
      setUser({
        ...user,
        profile: {
          ...user.profile,
          first_name: data.firstName,
          last_name: data.lastName,
          organization: data.organization || null,
          phone: data.phone || null,
        },
      });

      setSuccess('Profile updated successfully');
      setIsEditing(false);

      // Log activity
      await supabase.from('activities').insert({
        user_id: user.id,
        type: 'profile_update',
        description: 'Updated user profile',
        metadata: {
          updated_fields: Object.keys(data),
        },
      });

    } catch (error) {
      setError('Failed to update profile');
      console.error('Error updating profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Cancel editing
   */
  const cancelEdit = () => {
    if (user) {
      reset({
        firstName: user.profile.first_name,
        lastName: user.profile.last_name,
        organization: user.profile.organization || '',
        phone: user.profile.phone || '',
      });
    }
    setIsEditing(false);
    setError(null);
    setSuccess(null);
  };

  /**
   * Handle avatar upload
   */
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setError('Image size must be less than 5MB');
      return;
    }

    try {
      setIsSaving(true);
      setError(null);

      // Upload to Supabase storage
      const fileExt = file.name.split('.').pop();
      const fileName = `${user.id}/avatar.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('user-avatars')
        .upload(fileName, file, { upsert: true });

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-avatars')
        .getPublicUrl(fileName);

      // Update user profile with avatar URL
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({
          avatar_url: publicUrl,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', user.id);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setUser({
        ...user,
        profile: {
          ...user.profile,
          avatar_url: publicUrl,
        },
      });

      setSuccess('Avatar updated successfully');

    } catch (error) {
      setError('Failed to upload avatar');
      console.error('Error uploading avatar:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className={cn('text-center p-8', className)}>
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-gray-600">Failed to load user profile</p>
      </div>
    );
  }

  return (
    <div className={cn('bg-white rounded-lg shadow p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">User Profile</h2>
        {!isEditing && (
          <Button
            variant="outline"
            onClick={() => setIsEditing(true)}
            className="flex items-center"
          >
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
        )}
      </div>

      {/* Status Messages */}
      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Avatar Section */}
        <div className="lg:col-span-1">
          <div className="text-center">
            <div className="relative inline-block">
              {user.profile.avatar_url ? (
                <img
                  src={user.profile.avatar_url}
                  alt="User avatar"
                  className="w-32 h-32 rounded-full object-cover border-4 border-gray-200"
                />
              ) : (
                <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center border-4 border-gray-200">
                  <User className="h-16 w-16 text-gray-400" />
                </div>
              )}
              
              {/* Upload Button */}
              <label className="absolute bottom-0 right-0 bg-blue-500 hover:bg-blue-600 text-white rounded-full p-2 cursor-pointer shadow-lg">
                <Upload className="h-4 w-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                  disabled={isSaving}
                />
              </label>
            </div>
            
            <div className="mt-4">
              <h3 className="text-lg font-medium text-gray-900">
                {user.profile.first_name} {user.profile.last_name}
              </h3>
              <p className="text-sm text-gray-500">{user.email}</p>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                {getRoleDisplayName(user.role)}
              </span>
            </div>
          </div>
        </div>

        {/* Profile Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  disabled={!isEditing}
                  className={cn(
                    !isEditing && 'bg-gray-50',
                    errors.firstName && 'border-red-300'
                  )}
                  {...register('firstName')}
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  disabled={!isEditing}
                  className={cn(
                    !isEditing && 'bg-gray-50',
                    errors.lastName && 'border-red-300'
                  )}
                  {...register('lastName')}
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="organization">Organization</Label>
              <Input
                id="organization"
                type="text"
                disabled={!isEditing}
                className={cn(!isEditing && 'bg-gray-50')}
                placeholder="Your organization"
                {...register('organization')}
              />
            </div>

            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                disabled={!isEditing}
                className={cn(!isEditing && 'bg-gray-50')}
                placeholder="Your phone number"
                {...register('phone')}
              />
            </div>

            {/* Action Buttons */}
            {isEditing && (
              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={cancelEdit}
                  disabled={isSaving}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving || !isDirty}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
