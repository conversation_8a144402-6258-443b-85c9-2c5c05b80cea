# MEL/MMEL Evaluation System

A hyper-modern, AI-powered web application for aviation MEL (Minimum Equipment List) and MMEL (Master Minimum Equipment List) evaluation and compliance analysis.

## 🚀 Overview

This application provides aviation professionals with advanced tools to:
- Upload and analyze MEL/MMEL documents
- Perform AI-powered compliance checks
- Compare MEL against MMEL standards
- Generate detailed compliance reports
- Manage user roles and permissions
- Access contextual AI assistance through Mina

## 🏗️ Architecture

### Frontend Stack
- **Next.js 15+** - React framework with App Router
- **TypeScript** - Type-safe development
- **TailwindCSS** - Utility-first styling
- **Radix UI** - Accessible component primitives
- **Framer Motion** - Smooth animations
- **React Hook Form** - Form management

### Backend Stack
- **Supabase** - PostgreSQL database and authentication
- **Supabase Edge Functions** - Serverless backend logic
- **OpenRouter API** - Multi-LLM AI integration
- **Firebase Hosting** - Static site deployment

### AI Integration
- **DeepSeek** - Fast general analysis
- **LLaMA-4 Maverick** - Advanced reasoning
- **Mixtral** - Balanced document comparison

## 📁 Project Structure

```
melynx-v2/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Main dashboard
│   │   ├── upload/            # File upload interface
│   │   └── admin/             # Admin panel
│   ├── components/            # React components
│   │   ├── ui/                # Base UI components
│   │   ├── layout/            # Layout components
│   │   ├── upload/            # Upload-specific components
│   │   ├── auth/              # Authentication components
│   │   └── ai/                # AI-related components
│   ├── lib/                   # Utility libraries
│   │   ├── supabase/          # Database client and types
│   │   ├── ai/                # AI service integration
│   │   ├── auth/              # Authentication utilities
│   │   └── utils/             # General utilities
│   ├── types/                 # TypeScript type definitions
│   ├── hooks/                 # Custom React hooks
│   └── contexts/              # React contexts
├── supabase/
│   ├── functions/             # Edge Functions
│   ├── migrations/            # Database migrations
│   └── seed/                  # Seed data
├── docs/                      # Documentation
└── public/                    # Static assets
```

## 🔧 Setup Instructions

### Prerequisites
- Node.js 18+ and npm
- Supabase account and project
- OpenRouter API account
- Firebase account (for hosting)

### 1. Clone and Install
```bash
git clone <repository-url>
cd melynx-v2
npm install
```

### 2. Environment Configuration
```bash
cp .env.example .env.local
```

Fill in your environment variables:
- Supabase URL and keys
- OpenRouter API key
- Firebase configuration (optional)

### 3. Database Setup
```bash
# Install Supabase CLI
npm install -g supabase

# Initialize Supabase
supabase init

# Start local development
supabase start

# Apply migrations
supabase db push
```

### 4. Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🔐 Authentication & Authorization

### User Roles
- **Admin** - Full system access, user management
- **Inspector** - Upload, analyze, manage documents
- **Operator** - Upload and view documents, run analysis

### Protected Routes
- `/dashboard` - Requires authentication
- `/upload` - Requires authentication
- `/admin` - Requires admin role
- `/analysis/manage` - Requires inspector or admin role

## 📤 File Upload System

### Supported Formats
- PDF documents
- Microsoft Word (.doc, .docx)
- Plain text files

### Features
- Dual upload (MEL + MMEL simultaneously)
- Drag-and-drop interface
- Progress tracking
- Resumable uploads
- Automatic metadata extraction
- File validation and security checks

## 🤖 AI Analysis Engine

### Analysis Types
1. **Compliance Check** - Overall compliance scoring
2. **Clause Comparison** - Detailed clause-by-clause analysis
3. **Gap Analysis** - Identify missing or incorrect elements

### AI Models Integration
- **DeepSeek** - Fast initial analysis
- **LLaMA-4 Maverick** - Complex reasoning tasks
- **Mixtral** - Balanced performance for comparisons

### Output Blending
The system combines outputs from multiple models to provide:

- Higher accuracy through consensus
- Confidence scoring
- Comprehensive analysis coverage

## 🎨 Mina AI Assistant

### Features
- Floating avatar present on all pages
- Contextual help based on current page
- Chat interface for user queries
- Real-time assistance during uploads and analysis
- Intelligent responses about MEL/MMEL compliance

### Usage
- Click the floating avatar to open chat
- Ask questions about compliance, uploads, or navigation
- Get contextual help based on your current task
- Minimize/maximize chat window as needed

## 🚀 Deployment

### Firebase Hosting
```bash
# Build the application
npm run build

# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase
firebase init hosting

# Deploy
firebase deploy
```

### Environment Variables for Production
Ensure all environment variables are set in your hosting platform:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `OPENROUTER_API_KEY`
- `NEXT_PUBLIC_APP_URL`

## 🧪 Testing

### Running Tests
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

### Test Structure
- Unit tests for utilities and components
- Integration tests for API endpoints
- E2E tests for critical user flows
- Performance tests for file uploads

## 📊 Monitoring and Analytics

### Performance Monitoring
- File upload progress tracking
- AI analysis performance metrics
- User interaction analytics
- Error tracking and reporting

### Logging
- Structured logging with different levels
- User activity tracking
- System performance metrics
- Security event monitoring

## 🔒 Security Considerations

### Data Protection
- All files encrypted in transit and at rest
- Role-based access control
- Secure file upload validation
- API rate limiting

### Authentication Security
- Supabase Auth with JWT tokens
- Password strength requirements
- Session management
- Multi-factor authentication support

## 🛠️ Development Guidelines

### Code Style
- TypeScript strict mode enabled
- ESLint and Prettier configured
- Consistent naming conventions
- Comprehensive type definitions

### Component Architecture
- Modular component design
- Reusable UI components
- Custom hooks for logic separation
- Context providers for state management

### API Design
- RESTful API endpoints
- Consistent error handling
- Input validation and sanitization
- Comprehensive API documentation

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `POST /api/auth/reset-password` - Password reset

### File Upload Endpoints
- `POST /api/upload/initiate` - Initiate file upload
- `PUT /api/upload/complete` - Complete file upload
- `GET /api/upload/status/:id` - Get upload status
- `DELETE /api/upload/:id` - Delete uploaded file

### Analysis Endpoints
- `POST /api/analysis/start` - Start AI analysis
- `GET /api/analysis/:id` - Get analysis results
- `GET /api/analysis/history` - Get analysis history
- `DELETE /api/analysis/:id` - Delete analysis

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Review Process
- All changes require peer review
- Automated testing must pass
- Security review for sensitive changes
- Performance impact assessment

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Getting Help
- Check the documentation first
- Search existing issues
- Create a new issue with detailed information
- Contact the development team

### Common Issues
- Environment variable configuration
- Supabase connection problems
- File upload failures
- AI analysis timeouts

## 🔮 Roadmap

### Phase 1 (Current)
- ✅ Basic file upload system
- ✅ AI analysis integration
- ✅ User authentication
- ✅ Mina AI assistant

### Phase 2 (Next)
- Advanced analytics dashboard
- Batch document processing
- Custom compliance rules
- Integration with external systems

### Phase 3 (Future)
- Mobile application
- Offline analysis capabilities
- Advanced reporting features
- Machine learning model training

## 📞 Contact

For questions, support, or contributions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issue Tracker: [Link to issues]

---

**Built with ❤️ for the aviation industry by Adapty Air**
