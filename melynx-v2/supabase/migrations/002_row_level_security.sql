-- MEL/MMEL Evaluation System - Row Level Security Policies
-- This migration implements comprehensive RLS policies for role-based access control

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mel_clauses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user role
CREATE OR REPLACE FUNCTION public.get_user_role(user_uuid UUID)
RETURNS user_role AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.is_admin()
<PERSON><PERSON><PERSON><PERSON> BOOLEAN AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = auth.uid()) = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is inspector or admin
CREATE OR REPLACE FUNCTION public.is_inspector_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = auth.uid()) IN ('inspector', 'admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own record" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (is_admin());

CREATE POLICY "Users can update their own record" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can update any user" ON public.users
    FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can delete users" ON public.users
    FOR DELETE USING (is_admin());

-- User profiles table policies
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and inspectors can view all profiles" ON public.user_profiles
    FOR SELECT USING (is_inspector_or_admin());

CREATE POLICY "Users can insert their own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can update any profile" ON public.user_profiles
    FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can delete profiles" ON public.user_profiles
    FOR DELETE USING (is_admin());

-- File uploads table policies
CREATE POLICY "Users can view their own uploads" ON public.file_uploads
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and inspectors can view all uploads" ON public.file_uploads
    FOR SELECT USING (is_inspector_or_admin());

CREATE POLICY "Authenticated users can insert uploads" ON public.file_uploads
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own uploads" ON public.file_uploads
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins and inspectors can update any upload" ON public.file_uploads
    FOR UPDATE USING (is_inspector_or_admin());

CREATE POLICY "Users can delete their own uploads" ON public.file_uploads
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins and inspectors can delete any upload" ON public.file_uploads
    FOR DELETE USING (is_inspector_or_admin());

-- MEL documents table policies
CREATE POLICY "Users can view documents from their uploads" ON public.mel_documents
    FOR SELECT USING (
        file_upload_id IN (
            SELECT id FROM public.file_uploads WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and inspectors can view all documents" ON public.mel_documents
    FOR SELECT USING (is_inspector_or_admin());

CREATE POLICY "Users can insert documents for their uploads" ON public.mel_documents
    FOR INSERT WITH CHECK (
        file_upload_id IN (
            SELECT id FROM public.file_uploads WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert documents" ON public.mel_documents
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own documents" ON public.mel_documents
    FOR UPDATE USING (
        file_upload_id IN (
            SELECT id FROM public.file_uploads WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and inspectors can update any document" ON public.mel_documents
    FOR UPDATE USING (is_inspector_or_admin());

CREATE POLICY "Users can delete their own documents" ON public.mel_documents
    FOR DELETE USING (
        file_upload_id IN (
            SELECT id FROM public.file_uploads WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and inspectors can delete any document" ON public.mel_documents
    FOR DELETE USING (is_inspector_or_admin());

-- MEL clauses table policies
CREATE POLICY "Users can view clauses from their documents" ON public.mel_clauses
    FOR SELECT USING (
        document_id IN (
            SELECT md.id FROM public.mel_documents md
            JOIN public.file_uploads fu ON md.file_upload_id = fu.id
            WHERE fu.user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and inspectors can view all clauses" ON public.mel_clauses
    FOR SELECT USING (is_inspector_or_admin());

CREATE POLICY "System can insert clauses" ON public.mel_clauses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update clauses from their documents" ON public.mel_clauses
    FOR UPDATE USING (
        document_id IN (
            SELECT md.id FROM public.mel_documents md
            JOIN public.file_uploads fu ON md.file_upload_id = fu.id
            WHERE fu.user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and inspectors can update any clause" ON public.mel_clauses
    FOR UPDATE USING (is_inspector_or_admin());

CREATE POLICY "Users can delete clauses from their documents" ON public.mel_clauses
    FOR DELETE USING (
        document_id IN (
            SELECT md.id FROM public.mel_documents md
            JOIN public.file_uploads fu ON md.file_upload_id = fu.id
            WHERE fu.user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and inspectors can delete any clause" ON public.mel_clauses
    FOR DELETE USING (is_inspector_or_admin());

-- AI analyses table policies
CREATE POLICY "Users can view their own analyses" ON public.ai_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins and inspectors can view all analyses" ON public.ai_analyses
    FOR SELECT USING (is_inspector_or_admin());

CREATE POLICY "Authenticated users can insert analyses" ON public.ai_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own analyses" ON public.ai_analyses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can update analyses" ON public.ai_analyses
    FOR UPDATE WITH CHECK (true);

CREATE POLICY "Users can delete their own analyses" ON public.ai_analyses
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins and inspectors can delete any analysis" ON public.ai_analyses
    FOR DELETE USING (is_inspector_or_admin());

-- Activities table policies
CREATE POLICY "Users can view their own activities" ON public.activities
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all activities" ON public.activities
    FOR SELECT USING (is_admin());

CREATE POLICY "Authenticated users can insert activities" ON public.activities
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "System can insert activities" ON public.activities
    FOR INSERT WITH CHECK (true);

-- Storage policies for file buckets
CREATE POLICY "Users can upload to their own folder in mel-documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'mel-documents' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can view their own files in mel-documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'mel-documents' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Admins and inspectors can view all mel-documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'mel-documents' AND
        is_inspector_or_admin()
    );

CREATE POLICY "Users can upload to their own folder in mmel-documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'mmel-documents' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can view their own files in mmel-documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'mmel-documents' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Admins and inspectors can view all mmel-documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'mmel-documents' AND
        is_inspector_or_admin()
    );

CREATE POLICY "Users can upload their own avatars" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'user-avatars' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Anyone can view avatars" ON storage.objects
    FOR SELECT USING (bucket_id = 'user-avatars');

CREATE POLICY "Users can upload to their own folder in analysis-reports" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'analysis-reports' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Users can view their own analysis reports" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'analysis-reports' AND
        (storage.foldername(name))[1] = auth.uid()::text
    );

CREATE POLICY "Admins and inspectors can view all analysis reports" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'analysis-reports' AND
        is_inspector_or_admin()
    );
